[package]
name = "sinopharm-mcp-server"
version = "0.1.0"
edition = "2024"

[dependencies]
tokio = { version = "1", features = ["full"] }
rmcp = { version = "0.7", features = ["server", "transport-streamable-http-server"] }
hyper = "1"
hyper-util = { version = "0", features = ["server", "service"] }
tower-http = "0.6"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
axum = "0.8"
tracing = "0.1"
tracing-subscriber = "0.3"
tokio-util = "0.7"
